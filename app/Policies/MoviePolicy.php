<?php

namespace <PERSON><PERSON><PERSON>\Cinema\Policies;

use <PERSON><PERSON><PERSON>\Cinema\Models\Movie;
use <PERSON><PERSON><PERSON>\Cinema\Models\User;

class MoviePolicy
{
    public function before(User $user, $ability): ?bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        // Модератори можуть переглядати та редагувати, але не видаляти
        if ($user->isModerator() && in_array($ability, ['viewAny', 'view', 'update'])) {
            return true;
        }

        return null;
    }

    /**
     * Усі користувачі (включаючи неавторизованих) можуть переглядати список фільмів.
     */
    public function viewAny(?User $user): bool
    {
        return true;
    }

    /**
     * Усі користувачі (включаючи неавторизованих) можуть переглядати публічні фільми, адміністратори — усі.
     */
    public function view(?User $user, Movie $movie): bool
    {
        return $movie->is_public || ($user && $user->isAdmin());
    }

    /**
     * Тільки адміністратор може створювати нові фільми.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Дозволяється, якщо користувач є адміністратором або власником фільму.
     */
    public function update(User $user, Movie $movie): bool
    {
        return $user->isAdmin() || $user->id === $movie->user_id;
    }

    /**
     * Тільки адміністратор може видаляти фільми (модератори не можуть).
     */
    public function delete(User $user, Movie $movie): bool
    {
        return $user->isAdmin();
    }

    /**
     * Тільки адміністратор може відновлювати фільми.
     */
    public function restore(User $user, Movie $movie): bool
    {
        return $user->isAdmin();
    }

    /**
     * Тільки адміністратор може остаточно видаляти фільми.
     */
    public function forceDelete(User $user, Movie $movie): bool
    {
        return $user->isAdmin();
    }
}
