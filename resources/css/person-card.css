/* Person Card Component */
.person-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: row; /* Горизонтальний макет */
    align-items: stretch;
    height: auto; /* Висота адаптується до вмісту */
}

.person-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.person-card__link {
    display: flex;
    flex-direction: row;
    width: 100%;
    text-decoration: none; /* Видаляємо підкреслення посилання */
}

.person-card__image {
    width: 150px; /* Фіксована ширина для зображення */
    flex-shrink: 0;
    background: var(--background-secondary);
    position: relative;
    overflow: hidden;
}

.person-card__photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.person-card:hover .person-card__photo {
    transform: scale(1.05); /* Легке збільшення при наведенні */
}

.person-card__content {
    padding: var(--spacing-md);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    justify-content: center;
}

.person-card__name {
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.person-card__character {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    font-style: italic;
}

.person-card__type,
.person-card__birth-date,
.person-card__birthplace,
.person-card__gender {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    line-height: 1.2;
    font-weight: 500;
}

.person-card__birth-date i,
.person-card__birthplace i,
.person-card__gender i {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: 1;
}

/* Persons Grid */
.persons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

/* Dark Theme */
[data-theme="dark"] .person-card {
    background: var(--dark-card-background);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .person-card:hover {
    border-color: var(--primary-color);
}

[data-theme="dark"] .person-card__name {
    color: var(--dark-text-color);
}

[data-theme="dark"] .person-card__type,
[data-theme="dark"] .person-card__birth-date,
[data-theme="dark"] .person-card__birthplace,
[data-theme="dark"] .person-card__gender,
[data-theme="dark"] .person-card__birth-date i,
[data-theme="dark"] .person-card__birthplace i,
[data-theme="dark"] .person-card__gender i {
    color: var(--dark-text-muted);
}

/* Responsiveness */
@media (max-width: var(--breakpoint-tablet)) {
    .persons-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-sm);
    }

    .person-card__image {
        width: 120px; /* Зменшена ширина для планшетів */
    }

    .person-card__content {
        padding: var(--spacing-sm);
    }

    .person-card__name {
        font-size: var(--font-size-sm);
    }

    .person-card__character {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: var(--breakpoint-mobile)) {
    .persons-grid {
        grid-template-columns: 1fr; /* Одна колонка для мобільних */
    }

    .person-card__image {
        width: 100px; /* Ще менша ширина для маленьких екранів */
    }

    .person-card__content {
        padding: var(--spacing-xs);
    }

    .person-card__name {
        font-size: var(--font-size-sm);
    }

    .person-card__type,
    .person-card__birth-date,
    .person-card__birthplace,
    .person-card__gender {
        font-size: 11px; /* Трохи менший шрифт для економії простору */
    }
}
