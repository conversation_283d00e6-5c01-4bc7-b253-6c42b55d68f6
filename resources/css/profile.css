/* Стилі для сторінки профілю користувача */
.user-profile {
    min-height: 100vh;
    background-color: var(--background-color);
    color: var(--text-color);
    display: flex;
    flex-direction: column;
    font-family: var(--font-family); /* Explicitly set Inter font */
}

.user-profile__container {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    width: 100%;
    font-family: var(--font-family);
}

/* Шапка профілю */
.user-profile__header {
    position: relative;
    margin-bottom: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background-color: var(--card-background);
    box-shadow: var(--box-shadow);
}

.user-profile__cover {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    position: relative;
}

.user-profile__user {
    display: flex;
    padding: var(--spacing-lg);
    position: relative;
}

.user-profile__avatar-container {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--card-background);
    margin-top: -60px;
    position: relative;
    z-index: 1;
    background-color: var(--card-background);
    box-shadow: var(--box-shadow);
}

.user-profile__avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-profile__avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 48px;
    font-weight: var(--font-weight-bold);
    font-family: var(--font-family);
}

.user-profile__info {
    margin-left: var(--spacing-lg);
    flex: 1;
}

.user-profile__name {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
    font-family: var(--font-family);
}

.user-profile__badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.user-profile__badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family);
}

.user-profile__badge--verified {
    background-color: var(--success-color);
    color: var(--white);
}

.user-profile__badge--admin {
    background-color: var(--warning-color);
    color: var(--white);
}

.user-profile__badge--moderator {
    background-color: var(--primary-color);
    color: var(--white);
}

.user-profile__badge-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
}

.user-profile__provider {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-family: var(--font-family);
}

/* Контент профілю */
.user-profile__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Карта форми */
.user-profile__form-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
}

.user-profile__form-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
    font-family: var(--font-family);
}

/* Форма профілю */
.user-profile__form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.user-profile__form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.user-profile__field {
    display: flex;
    flex-direction: column;
}

.user-profile__field--full {
    grid-column: 1 / -1;
}

.user-profile__label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    color: var(--text-muted);
    font-family: var(--font-family);
}

.user-profile__input,
.user-profile__textarea,
.user-profile__select {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--input-background);
    color: var(--text-color);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: border-color var(--transition-duration) var(--transition-easing), box-shadow var(--transition-duration) var(--transition-easing);
}

.user-profile__input:focus,
.user-profile__textarea:focus,
.user-profile__select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
    outline: none;
}

.user-profile__textarea {
    resize: vertical;
    min-height: 100px;
}

.user-profile__select {
    width: 100%;
}

.user-profile__input-wrapper {
    position: relative;
}

.user-profile__input-badge {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
}

.user-profile__input-badge-icon {
    width: 16px;
    height: 16px;
    color: var(--success-color);
}

.user-profile__error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    font-family: var(--font-family);
}

.user-profile__counter {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-align: right;
    margin-top: var(--spacing-xs);
    font-family: var(--font-family);
}

.user-profile__photos {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.user-profile__photo {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    position: relative;
}

.user-profile__photo--backdrop .user-profile__photo-preview,
.user-profile__photo--backdrop .user-profile__photo-placeholder {
    width: 200px;
    height: 100px;
    border-radius: var(--border-radius-md);
}

.user-profile__photo-preview {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

.user-profile__photo-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: var(--white);
    font-size: 36px;
    font-weight: var(--font-weight-bold);
    font-family: var(--font-family);
}

.user-profile__photo-placeholder--backdrop {
    flex-direction: column;
    font-size: var(--font-size-sm);
    font-family: var(--font-family);
}

.user-profile__photo-placeholder-icon {
    width: 24px;
    height: 24px;
    margin-bottom: var(--spacing-xs);
}

.user-profile__photo-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.user-profile__photo-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-family: var(--font-family);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-duration) var(--transition-easing);
    text-align: center;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
    position: relative;
    overflow: hidden;
}

.user-profile__photo-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    transition: left 0.5s ease;
}

.user-profile__photo-button:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.user-profile__photo-button:hover::before {
    left: 100%;
}

.user-profile__photo-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.user-profile__photo-icon {
    width: 16px;
    height: 16px;
}

.user-profile__photo-input {
    display: none;
}

.user-profile__actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

.user-profile__button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: background-color var(--transition-duration) var(--transition-easing);
}

.user-profile__button:hover {
    background-color: var(--accent-color);
}

.user-profile__button-icon {
    width: 16px;
    height: 16px;
}

.user-profile__alert {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    font-family: var(--font-family);
}

.user-profile__alert--success {
    background-color: var(--success-light);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.user-profile__alert-icon {
    width: 16px;
    height: 16px;
}

/* Адаптивність */
@media (max-width: 992px) {
    .user-profile__content {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .user-profile__user {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .user-profile__info {
        margin-left: 0;
        margin-top: var(--spacing-md);
    }

    .user-profile__form-grid {
        grid-template-columns: 1fr;
    }

    .user-profile__settings-grid {
        grid-template-columns: 1fr;
    }

    .user-profile__photos {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-profile__photo--backdrop .user-profile__photo-preview,
    .user-profile__photo--backdrop .user-profile__photo-placeholder {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 576px) {
    .user-profile__avatar-container {
        width: 80px;
        height: 80px;
        margin-top: -40px;
    }

    .user-profile__avatar-placeholder {
        font-size: 32px;
    }

    .user-profile__photo-preview,
    .user-profile__photo-placeholder {
        width: 80px;
        height: 80px;
    }

    .user-profile__photo-placeholder--backdrop {
        font-size: var(--font-size-xs);
    }
}

/* Admin Actions */
.user-profile__admin-actions {
    margin-top: var(--spacing-sm);
}

.user-profile__admin-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    color: var(--white);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family);
    transition: all var(--transition-duration) var(--transition-easing);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

.user-profile__admin-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    background: linear-gradient(135deg, var(--warning-dark), var(--warning-color));
    color: var(--white);
    text-decoration: none;
}

.user-profile__admin-icon {
    width: 18px;
    height: 18px;
}

/* Налаштування користувача */
.user-profile__settings {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.user-profile__settings-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
    font-family: var(--font-family);
}

.user-profile__settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
}

.user-profile__setting {
    display: flex;
    flex-direction: column;
}

.user-profile__setting-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-duration) var(--transition-easing);
    font-family: var(--font-family);
}

.user-profile__setting-label:hover {
    background-color: var(--hover-background);
}

.user-profile__setting-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--input-background);
    cursor: pointer;
    transition: all var(--transition-duration) var(--transition-easing);
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.user-profile__setting-checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.user-profile__setting-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 14px;
    font-weight: var(--font-weight-bold);
}

.user-profile__setting-checkbox:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.user-profile__setting-text {
    font-size: var(--font-size-base);
    color: var(--text-color);
    font-family: var(--font-family);
    line-height: 1.4;
}

/* Alert Error State */
.user-profile__alert--error {
    background-color: var(--error-light);
    border: 1px solid var(--error-color);
    color: var(--error-color);
}

/* Upload Loading Animations */
.user-profile__upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.98));
    backdrop-filter: blur(12px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    z-index: 10;
    animation: fadeInUpload 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.user-profile__upload-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.3);
}

.user-profile__upload-spinner::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color), var(--primary-color));
    border-radius: 50%;
    z-index: -1;
    animation: rotateGradient 3s linear infinite;
}

.user-profile__spinner-icon {
    width: 100%;
    height: 100%;
    animation: rotateSpinner 1.5s linear infinite;
    background: var(--card-background);
    border-radius: 50%;
    padding: 12px;
}

.user-profile__spinner-icon--small {
    width: 24px;
    height: 24px;
    padding: 4px;
}

.user-profile__spinner-path {
    stroke: var(--primary-color);
    stroke-dasharray: 120, 200;
    stroke-dashoffset: 0;
    stroke-linecap: round;
    stroke-width: 3;
    animation: dashSpinner 2s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(var(--primary-rgb), 0.3));
}

.user-profile__upload-text {
    text-align: center;
    color: var(--text-color);
    font-family: var(--font-family);
    animation: pulseText 2s ease-in-out infinite;
}

.user-profile__upload-title {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-profile__upload-subtitle {
    display: block;
    font-size: var(--font-size-base);
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
    opacity: 0.8;
}

/* Button Loading States */
.user-profile__button-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-duration) var(--transition-easing);
}

.user-profile__button-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile__button-text {
    transition: all var(--transition-duration) var(--transition-easing);
}

.user-profile__button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

/* Progress Bar Animation */
.user-profile__upload-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: rgba(var(--primary-rgb), 0.15);
    overflow: hidden;
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-profile__upload-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    animation: shimmer 2s ease-in-out infinite;
}

.user-profile__upload-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    animation: progressBarMove 2.5s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.5);
}

.user-profile__photo-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* Keyframe Animations */
@keyframes fadeInUpload {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
        filter: blur(4px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0);
    }
}

@keyframes rotateSpinner {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes rotateGradient {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes dashSpinner {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 120, 200;
        stroke-dashoffset: -60;
    }
    100% {
        stroke-dasharray: 120, 200;
        stroke-dashoffset: -180;
    }
}

@keyframes progressBarMove {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes pulseText {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

/* Pulse Animation for Loading States */
@keyframes pulseUpload {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.user-profile__upload-overlay {
    animation: fadeInUpload 0.3s ease-in-out, pulseUpload 2s ease-in-out infinite;
}

/* Dark theme adjustments */
[data-theme="dark"] .user-profile__upload-overlay {
    background: linear-gradient(135deg, rgba(18, 18, 18, 0.98), rgba(30, 30, 30, 0.98));
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .user-profile__upload-title {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="dark"] .user-profile__admin-button {
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.4);
}

[data-theme="dark"] .user-profile__admin-button:hover {
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
}

[data-theme="dark"] .user-profile__photo-button {
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.4);
}

[data-theme="dark"] .user-profile__photo-button:hover {
    box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.5);
}
