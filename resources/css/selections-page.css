/* Block: selections-page */
.selections-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Remove margin after footer */
.selections-page .main-footer {
    margin-top: 0;
}

/* Element: filters */
.selections-page__filters {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Modifier: dark theme for filters */
[data-theme="dark"] .selections-page__filters {
    background-color: var(--dark-card-background);
    box-shadow: 0 4px 12px var(--dark-shadow-color);
}

/* Element: top-panel */
.selections-page__top-panel {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

/* Element: search */
.selections-page__search {
    position: relative;
    flex: 1;
}

.selections-page__search::before {
    position: absolute;
    top: 50%;
    left: 12px;
    width: 18px;
    height: 18px;
    content: "";
    background-image: url('../../public/images/search-icon.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.7;
    transform: translateY(-50%);
}

/* Element: search-input */
.selections-page__search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 40px;
    font-size: var(--font-size-base);
    color: var(--text-color);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.selections-page__search-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

/* Modifier: dark theme for search-input */
[data-theme="dark"] .selections-page__search-input {
    color: var(--dark-text-color);
    background-color: var(--dark-card-background);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .selections-page__search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
}

/* Element: filters-group */
.selections-page__filters-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

/* Element: filter-item */
.selections-page__filter-item {
    position: relative;
}

/* Element: filter-label */
.selections-page__filter-label {
    display: block;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Element: select-wrapper */
.selections-page__select-wrapper {
    position: relative;
    width: 100%;
}

/* Element: select */
.selections-page__select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: calc(var(--spacing-md) * 2);
    font-size: var(--font-size-base);
    color: var(--text-color);
    appearance: none;
    cursor: pointer;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

.selections-page__select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

/* Element: select-arrow */
.selections-page__select-arrow {
    position: absolute;
    top: 50%;
    right: var(--spacing-md);
    width: 10px;
    height: 10px;
    pointer-events: none;
    border-color: var(--text-muted) transparent transparent transparent;
    border-style: solid;
    border-width: 6px 5px 0;
    transition: border-color 0.3s ease;
    transform: translateY(-50%);
}

.selections-page__select:focus + .selections-page__select-arrow {
    border-color: var(--primary-color) transparent transparent transparent;
}

/* Modifier: dark theme for select */
[data-theme="dark"] .selections-page__select {
    color: var(--dark-text-color);
    background-color: var(--dark-card-background);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .selections-page__select option {
    color: var(--dark-text-color);
    background-color: var(--dark-card-background);
}

[data-theme="dark"] .selections-page__select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.3);
}

[data-theme="dark"] .selections-page__select-arrow {
    border-color: var(--dark-text-muted) transparent transparent transparent;
}

[data-theme="dark"] .selections-page__select:focus + .selections-page__select-arrow {
    border-color: var(--primary-color) transparent transparent transparent;
}

/* Element: actions */
.selections-page__actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Element: reset-button */
.selections-page__reset-button {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    white-space: nowrap;
    cursor: pointer;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.selections-page__reset-button::before {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    content: "";
    background-color: currentcolor;
    mask: url('../../public/images/reset-icon.svg') no-repeat center;
    mask-size: contain;
}

.selections-page__reset-button:hover {
    color: var(--text-color);
    background-color: rgba(var(--primary-rgb), 0.05);
    border-color: var(--text-color);
}

/* Element: apply-button */
.selections-page__apply-button {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: white;
    white-space: nowrap;
    cursor: pointer;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.selections-page__apply-button::before {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    content: "";
    background-color: currentcolor;
    mask: url('../../public/images/check-icon.svg') no-repeat center;
    mask-size: contain;
}

.selections-page__apply-button:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Element: sort */
.selections-page__sort {
    padding-bottom: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

/* Element: sort-label */
.selections-page__sort-label {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Element: sort-icon */
.selections-page__sort-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    background-color: currentcolor;
    mask: url('../../public/images/sort-icon.svg') no-repeat center;
    mask-size: contain;
}

/* Element: sort-buttons */
.selections-page__sort-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

/* Element: sort-button */
.selections-page__sort-button {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    cursor: pointer;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.selections-page__sort-button:hover {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
    border-color: var(--primary-color);
}

/* Modifier: active for sort-button */
.selections-page__sort-button--active {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Element: sort-direction */
.selections-page__sort-direction {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.selections-page__sort-direction::after {
    font-size: 14px;
    line-height: 1;
    content: "↑";
}

.selections-page__sort-direction--desc::after {
    content: "↓";
}

.selections-page__sort-button--active:hover {
    color: white;
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Modifier: dark theme for sort-button */
[data-theme="dark"] .selections-page__sort-button {
    color: var(--dark-text-color);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .selections-page__sort-button:hover {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: var(--primary-color);
}

/* Block: selections-grid */
.selections-grid {
    position: relative;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* Стилі для порожньої сторінки підбірок */
.selections-page-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    grid-column: 1 / -1;
}

.selections-page-empty-text {
    font-size: var(--font-size-lg);
    color: var(--text-muted);
}

/* Block: selection-card */
.selection-card {
    position: relative;
    z-index: 1;
    display: block;
    height: 100%;
    overflow: hidden;
    text-decoration: none;
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.selection-card:hover {
    box-shadow: 0 10px 20px var(--shadow-color);
    transform: translateY(-5px);
}

/* Element: image-wrapper */
.selection-card__image-wrapper {
    position: relative;
    height: 220px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1a1a1a;
}

/* Element: poster */
.selection-card__poster {
    position: absolute;
    width: 120px;
    height: 170px;
    overflow: hidden;
    background-color: #333333;
    border-radius: var(--border-radius-md);
    box-shadow: 0 10px 20px rgb(0 0 0 / 50%);
    transition: transform 0.5s ease, box-shadow 0.5s ease;
}

/* Element: poster-image */
.selection-card__poster-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Modifiers: poster positions */
.selection-card__poster--1 {
    z-index: 3;
    box-shadow: 0 10px 20px rgb(0 0 0 / 50%), 0 0 15px rgb(255 0 255 / 50%);
    transform: translateX(-90px) rotateY(12deg) rotateZ(-6deg) scale(1.03);
}

.selection-card__poster--2 {
    z-index: 4;
    box-shadow: 0 10px 30px rgb(0 0 0 / 70%), 0 0 20px rgb(0 255 255 / 50%);
    transform: translateX(0) rotateY(0deg) rotateZ(0deg) scale(1.05);
}

.selection-card__poster--3 {
    z-index: 2;
    box-shadow: 0 10px 20px rgb(0 0 0 / 50%), 0 0 15px rgb(255 255 0 / 50%);
    transform: translateX(90px) rotateY(-12deg) rotateZ(6deg) scale(1.03);
}

/* Element: overlay */
.selection-card__overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, rgb(0 0 0 / 10%), rgb(0 0 0 / 70%));
}

/* Element: count */
.selection-card__count {
    position: absolute;
    right: var(--spacing-sm);
    bottom: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--white);
    background-color: rgb(0 0 0 / 70%);
    border-radius: var(--border-radius-sm);
    z-index: 10;
}

/* Element: types */
.selection-card__types {
    position: absolute;
    left: var(--spacing-sm);
    bottom: var(--spacing-sm);
    display: flex;
    gap: var(--spacing-xs);
    z-index: 10;
}

/* Element: type */
.selection-card__type {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--white);
    background-color: rgb(0 0 0 / 70%);
    border-radius: var(--border-radius-sm);
}

/* Modifiers: type variants */
.selection-card__type--movies {
    background-color: rgba(var(--primary-rgb), 0.8);
}

.selection-card__type--persons {
    background-color: rgba(var(--success-rgb, 16, 185, 129), 0.8);
}

.selection-card__type--episodes {
    background-color: rgba(var(--warning-rgb, 245, 158, 11), 0.8);
}

/* Element: content */
.selection-card__content {
    padding: var(--spacing-md);
}

/* Element: title */
.selection-card__title {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-color);
}

/* Element: description */
.selection-card__description {
    display: -webkit-box;
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--text-muted);
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* Element: meta */
.selection-card__meta {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Elements: date, author */
.selection-card__date,
.selection-card__author {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Modifier: dark theme for selection-card */
[data-theme="dark"] .selection-card {
    background-color: var(--dark-card-background);
    box-shadow: var(--dark-box-shadow);
}

[data-theme="dark"] .selection-card:hover {
    box-shadow: 0 10px 20px var(--dark-shadow-color);
}

[data-theme="dark"] .selection-card__title {
    color: var(--dark-text-color);
}

[data-theme="dark"] .selection-card__description {
    color: var(--dark-text-muted);
}

[data-theme="dark"] .selection-card__meta {
    color: var(--dark-text-muted);
}

/* Block: selection-show */
.selection-show {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Element: header */
.selection-show__header {
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    width: 100%;
}

/* Element: image */
.selection-show__image {
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-radius: var(--border-radius-md);
}

/* Element: image-img */
.selection-show__image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Element: description */
.selection-show__description {
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
    box-sizing: border-box;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

/* Element: meta */
.selection-show__meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: auto;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Elements: date, author, count */
.selection-show__date,
.selection-show__author,
.selection-show__count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Element: section */
.selection-show__section {
    margin-bottom: var(--spacing-xl);
}

/* Element: section-title */
.selection-show__section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-color);
}

/* Element: empty */
.selection-show__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
}

/* Element: empty-text */
.selection-show__empty-text {
    font-size: var(--font-size-lg);
    color: var(--text-muted);
}

/* Modifier: dark theme for selection-show */
[data-theme="dark"] .selection-show__header {
    background-color: var(--dark-card-background);
    box-shadow: var(--dark-box-shadow);
}

[data-theme="dark"] .selection-show__description {
    color: var(--dark-text-color);
}

[data-theme="dark"] .selection-show__meta {
    border-color: var(--dark-border-color);
    color: var(--dark-text-muted);
}

[data-theme="dark"] .selection-show__section-title {
    color: var(--dark-text-color);
}

[data-theme="dark"] .selection-show__empty {
    background-color: var(--dark-card-background);
    box-shadow: var(--dark-box-shadow);
}

[data-theme="dark"] .selection-show__empty-text {
    color: var(--dark-text-muted);
}

/* Медіа-запити для адаптивності */
@media (max-width: 768px) {
    .selection-show__header {
        grid-template-columns: 1fr;
    }

    .selections-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 576px) {
    .selections-page__top-panel {
        flex-direction: column;
        align-items: stretch;
    }

    .selections-page__actions {
        margin-top: var(--spacing-sm);
    }
}

