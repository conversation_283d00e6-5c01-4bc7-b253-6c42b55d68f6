/* Стилі для секції акторського складу */
.movie-cast {
    margin: 3rem 0;
    padding: 2rem 0;
}

.movie-cast__header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}
/* Block: movie-cast */
.movie-cast {
    margin-bottom: var(--spacing-xl);
}

/* Element: movie-cast__header */
.movie-cast__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

/* Element: movie-cast__title */
.movie-cast__title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.movie-cast__title i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

/* Element: movie-cast__grid */
.movie-cast__grid {
    position: relative;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* Стилі для person-card */
.person-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: row;
    align-items: stretch;
    height: auto;
}

.person-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.person-card__link {
    display: flex;
    flex-direction: row;
    width: 100%;
    text-decoration: none;
}

.person-card__image {
    width: 150px;
    flex-shrink: 0;
    background: var(--background-secondary);
    position: relative;
    overflow: hidden;
}

.person-card__photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.person-card:hover .person-card__photo {
    transform: scale(1.05);
}

.person-card__content {
    padding: var(--spacing-md);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    justify-content: center;
}

.person-card__name {
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1.3;
}

.person-card__character {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    font-style: italic;
}

.person-card__type,
.person-card__birth-date,
.person-card__birthplace,
.person-card__gender {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    line-height: 1.2;
    font-weight: 500;
}

.person-card__birth-date i,
.person-card__birthplace i,
.person-card__gender i {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: 1;
}

/* Темна тема */
[data-theme="dark"] .movie-cast__title {
    color: var(--dark-text-color);
}

[data-theme="dark"] .movie-cast__title i {
    color: var(--primary-color);
}

[data-theme="dark"] .person-card {
    background: var(--dark-card-background);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .person-card:hover {
    border-color: var(--primary-color);
}

[data-theme="dark"] .person-card__name {
    color: var(--dark-text-color);
}

[data-theme="dark"] .person-card__type,
[data-theme="dark"] .person-card__birth-date,
[data-theme="dark"] .person-card__birthplace,
[data-theme="dark"] .person-card__gender,
[data-theme="dark"] .person-card__birth-date i,
[data-theme="dark"] .person-card__birthplace i,
[data-theme="dark"] .person-card__gender i {
    color: var(--dark-text-muted);
}

/* Адаптивність */
@media (max-width: var(--breakpoint-desktop)) {
    .movie-cast__grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: var(--breakpoint-tablet)) {
    .movie-cast__grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-sm);
    }

    .movie-cast__title {
        font-size: var(--font-size-lg);
    }

    .movie-cast__title i {
        font-size: var(--font-size-base);
    }

    .person-card__image {
        width: 120px;
    }

    .person-card__content {
        padding: var(--spacing-sm);
    }

    .person-card__name {
        font-size: var(--font-size-sm);
    }

    .person-card__character {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: var(--breakpoint-mobile)) {
    .movie-cast__grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .movie-cast__title {
        font-size: var(--font-size-base);
    }

    .movie-cast__title i {
        font-size: var(--font-size-sm);
    }

    .person-card__image {
        width: 100px;
    }

    .person-card__content {
        padding: var(--spacing-xs);
    }

    .person-card__name {
        font-size: var(--font-size-sm);
    }

    .person-card__type,
    .person-card__birth-date,
    .person-card__birthplace,
    .person-card__gender {
        font-size: 11px;
    }
}
