/* Person Detail Page - WOW Design */
.person-detail {
    background: linear-gradient(145deg, var(--card-background) 0%, rgba(var(--primary-rgb), 0.02) 100%);
    border-radius: var(--border-radius-xxl);
    box-shadow:
        0 20px 60px rgba(var(--primary-rgb), 0.1),
        0 8px 25px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    margin-top: var(--spacing-lg);
    overflow: hidden;
    position: relative;
    animation: slideInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.person-detail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

/* Header Section */
.person-detail__header {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background:
        linear-gradient(135deg,
            rgba(var(--primary-rgb), 0.03) 0%,
            rgba(var(--accent-rgb), 0.02) 50%,
            rgba(var(--primary-rgb), 0.01) 100%
        );
    position: relative;
    overflow: hidden;
}

.person-detail__header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(var(--primary-rgb), 0.05) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

/* Image Container */
.person-detail__image {
    align-self: center;
    width: 200px;
    max-width: 100%;
    aspect-ratio: 3 / 4;
    overflow: hidden;
    border-radius: var(--border-radius-xl);
    background: linear-gradient(145deg, var(--background-tertiary), var(--background-secondary));
    box-shadow:
        0 15px 35px rgba(var(--primary-rgb), 0.15),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    border: 2px solid rgba(var(--primary-rgb), 0.1);
}

.person-detail__image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.person-detail__image:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(var(--primary-rgb), 0.25),
        0 10px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.person-detail__image:hover::before {
    opacity: 1;
}

.person-detail__photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    position: relative;
    z-index: 0;
}

.person-detail__photo:hover {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.05);
}

/* Person Info */
.person-detail__info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
}

.person-detail__title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1.2;
}

/* Meta Information */
.person-detail__meta-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    flex-wrap: wrap;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--accent-rgb), 0.03) 100%);
    border: 1px solid rgba(var(--primary-rgb), 0.08);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.person-detail__meta-item:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.08) 0%, rgba(var(--accent-rgb), 0.05) 100%);
    border-color: rgba(var(--primary-rgb), 0.15);
    box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.1);
}

.person-detail__meta-icon {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    flex-shrink: 0;
    filter: drop-shadow(0 2px 4px rgba(var(--primary-rgb), 0.3));
    transition: all 0.3s ease;
}

.person-detail__meta-item:hover .person-detail__meta-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(var(--primary-rgb), 0.4));
}

.person-detail__meta-label {
    font-weight: 600;
    color: var(--text-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.person-detail__meta-value {
    color: var(--text-muted);
    font-weight: 500;
}

/* Content Section */
.person-detail__content {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Section Titles */
.person-detail__section-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 var(--spacing-md) 0;
    padding-bottom: var(--spacing-xs);
    border-bottom: 2px solid var(--primary-color);
    display: inline-block;
}

/* Biography Section in Card */
.person-detail__biography {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.person-detail__biography-title {
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 var(--spacing-sm) 0;
}

.person-detail__biography-text {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    line-height: var(--line-height);
    text-align: justify;
}

/* Movies Section */
.person-detail__movies {
    margin-bottom: var(--spacing-lg);
}

.person-detail__movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-md);
}

/* Empty State */
.person-detail__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.person-detail__empty-content {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    border: 2px dashed var(--border-color);
    max-width: 400px;
}

.person-detail__empty-text {
    font-size: var(--font-size-base);
    color: var(--text-muted);
    margin: 0;
    line-height: var(--line-height);
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .person-detail__header {
        padding: var(--spacing-md);
    }

    .person-detail__content {
        padding: var(--spacing-md);
    }

    .person-detail__image {
        width: 160px;
    }

    .person-detail__title {
        font-size: var(--font-size-lg);
    }

    .person-detail__meta-item {
        font-size: var(--font-size-xs);
        gap: var(--spacing-xs);
    }

    .person-detail__movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: var(--spacing-sm);
    }

    .person-detail__empty-content {
        padding: var(--spacing-lg);
        margin: var(--spacing-md);
    }
}

/* Tablet Styles */
@media (min-width: 768px) {
    .person-detail {
        display: flex;
        flex-direction: row;
        gap: var(--spacing-xl);
    }

    .person-detail__header {
        flex: 0 0 320px;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .person-detail__image {
        width: 280px;
    }

    .person-detail__title {
        font-size: 2rem;
        margin-bottom: var(--spacing-md);
    }

    .person-detail__content {
        flex: 1;
        padding-left: 0;
    }

    .person-detail__movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (min-width: 1024px) {
    .person-detail__header {
        flex: 0 0 360px;
        padding: var(--spacing-xl);
    }

    .person-detail__content {
        padding: var(--spacing-xl);
    }

    .person-detail__image {
        width: 320px;
    }

    .person-detail__movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark Theme */
[data-theme="dark"] .person-detail {
    background-color: var(--dark-card-background);
}

[data-theme="dark"] .person-detail__header {
    background: linear-gradient(135deg, var(--dark-card-background) 0%, var(--dark-background) 100%);
}

[data-theme="dark"] .person-detail__image {
    background-color: var(--dark-border-color);
}

[data-theme="dark"] .person-detail__empty-content {
    background-color: var(--dark-background);
    border-color: var(--dark-border-color);
}
