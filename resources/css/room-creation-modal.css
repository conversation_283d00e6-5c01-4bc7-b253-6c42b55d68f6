/* Стилі для компонента створення кімнати */
.room-creation-modal__trigger {
    margin: var(--spacing-xl) 0;
    display: flex;
    justify-content: center;
}

.room-creation-modal__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--primary-color);
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

.room-creation-modal__btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.4);
}



/* Модальне вікно */
.room-creation-modal__overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-lg);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.room-creation-modal__modal {
    background: var(--card-background);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 24px 64px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
    border: 1px solid var(--border-color);
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(32px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.room-creation-modal__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05), rgba(var(--accent-rgb), 0.05));
}

.room-creation-modal__title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-color);
}

.room-creation-modal__title i {
    color: var(--primary-color);
}

.room-creation-modal__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.room-creation-modal__close:hover {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
    transform: scale(1.1);
}

.room-creation-modal__body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.room-creation-modal__form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.room-creation-modal__form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.room-creation-modal__form-group--password {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    max-height: 0;
    margin: 0;
}

.room-creation-modal__form-group--password[style*="flex"] {
    opacity: 1;
    max-height: 200px;
    margin: var(--spacing-lg) 0 0 0;
}

.room-creation-modal__form-group--animated {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-16px);
        max-height: 0;
        margin-top: 0;
        margin-bottom: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 200px;
        margin-top: 0;
        margin-bottom: var(--spacing-lg);
    }
}

.room-creation-modal__form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-color);
}

.room-creation-modal__form-label i {
    color: var(--primary-color);
    width: 16px;
}

.room-creation-modal__form-input {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    color: var(--text-color);
    background: var(--background-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.room-creation-modal__form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background: var(--card-background);
}

.room-creation-modal__form-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.room-creation-modal__form-checkbox:hover {
    background: rgba(var(--primary-rgb), 0.05);
}

.room-creation-modal__checkbox-input {
    display: none;
}

.room-creation-modal__checkbox-custom {
    position: relative;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--background-color);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.room-creation-modal__checkbox-custom::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 4px;
    height: 8px;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg) scale(0.5);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.room-creation-modal__checkbox-input:checked + .room-creation-modal__checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.room-creation-modal__checkbox-input:checked + .room-creation-modal__checkbox-custom::after {
    opacity: 1;
    transform: rotate(45deg) scale(1);
}

.room-creation-modal__checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-color);
}

.room-creation-modal__checkbox-label i {
    color: var(--primary-color);
    width: 16px;
}

.room-creation-modal__form-error {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
    margin-top: var(--spacing-xs);
}

.room-creation-modal__alert {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.room-creation-modal__alert--error {
    background: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
    border: 1px solid rgba(var(--error-rgb), 0.3);
}

.room-creation-modal__actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.room-creation-modal__btn-secondary,
.room-creation-modal__btn-primary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.room-creation-modal__btn-secondary::before,
.room-creation-modal__btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.room-creation-modal__btn-secondary:hover::before,
.room-creation-modal__btn-primary:hover::before {
    left: 100%;
}

.room-creation-modal__btn-secondary {
    background: var(--background-color);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.room-creation-modal__btn-secondary:hover {
    background: rgba(var(--text-rgb), 0.05);
    border-color: var(--text-secondary);
    color: var(--text-color);
    transform: translateY(-2px);
}

.room-creation-modal__btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3);
}

.room-creation-modal__btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    box-shadow: 0 6px 24px rgba(var(--primary-rgb), 0.4);
    transform: translateY(-2px);
}

/* Адаптивність */
@media (max-width: 768px) {
    .room-creation-modal__btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .room-creation-modal__overlay {
        padding: var(--spacing-md);
    }

    .room-creation-modal__header {
        padding: var(--spacing-md);
    }

    .room-creation-modal__title {
        font-size: var(--font-size-base);
    }

    .room-creation-modal__body {
        padding: var(--spacing-md);
    }

    .room-creation-modal__actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .room-creation-modal__btn-secondary,
    .room-creation-modal__btn-primary {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .room-creation-modal__btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .room-creation-modal__overlay {
        padding: var(--spacing-sm);
    }

    .room-creation-modal__modal {
        max-height: 95vh;
    }

    .room-creation-modal__header {
        padding: var(--spacing-sm);
    }

    .room-creation-modal__title {
        font-size: var(--font-size-sm);
    }

    .room-creation-modal__body {
        padding: var(--spacing-sm);
    }

    .room-creation-modal__form-group {
        gap: var(--spacing-xs);
    }

    .room-creation-modal__actions {
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-md);
    }
}

/* Темна тема */
[data-theme="dark"] .room-creation-modal__btn {
    background: var(--primary-color);
    color: var(--white);
}

[data-theme="dark"] .room-creation-modal__btn:hover {
    background: var(--accent-color);
}

[data-theme="dark"] .room-creation-modal__modal {
    background: var(--dark-card-background);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .room-creation-modal__header {
    border-color: var(--dark-border-color);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.1), rgba(var(--accent-rgb), 0.1));
}

[data-theme="dark"] .room-creation-modal__title {
    color: var(--dark-text-color);
}

[data-theme="dark"] .room-creation-modal__close {
    color: var(--dark-text-secondary);
}

[data-theme="dark"] .room-creation-modal__close:hover {
    background: rgba(var(--error-rgb), 0.2);
    color: var(--error-color);
}

[data-theme="dark"] .room-creation-modal__form-label {
    color: var(--dark-text-color);
}

[data-theme="dark"] .room-creation-modal__form-input {
    background: var(--dark-background-color);
    border-color: var(--dark-border-color);
    color: var(--dark-text-color);
}

[data-theme="dark"] .room-creation-modal__form-input:focus {
    background: var(--dark-card-background);
    border-color: var(--primary-color);
}

[data-theme="dark"] .room-creation-modal__form-checkbox:hover {
    background: rgba(var(--primary-rgb), 0.1);
}

[data-theme="dark"] .room-creation-modal__checkbox-custom {
    background: var(--dark-background-color);
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .room-creation-modal__checkbox-label {
    color: var(--dark-text-color);
}

[data-theme="dark"] .room-creation-modal__actions {
    border-color: var(--dark-border-color);
}

[data-theme="dark"] .room-creation-modal__btn-secondary {
    background: var(--dark-background-color);
    border-color: var(--dark-border-color);
    color: var(--dark-text-secondary);
}

[data-theme="dark"] .room-creation-modal__btn-secondary:hover {
    background: rgba(var(--dark-text-rgb), 0.1);
    border-color: var(--dark-text-secondary);
    color: var(--dark-text-color);
}
