/* Стилі для заголовка */
.header {
    background-color: var(--card-background);
    box-shadow: 0 2px 10px var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.header__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
}

.header__logo {
    display: flex;
    align-items: center;
}

.header__logo-link {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.header__logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-left: var(--spacing-xs);
}

.header__nav {
    display: flex;
    margin-left: var(--spacing-xl);
}

.header__nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.header__nav-item {
    position: relative;
}

.header__nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: color 0.3s ease, background-color 0.3s ease;
}

.header__nav-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.header__nav-link--active {
    color: var(--primary-color);
    font-weight: 600;
}

.header__actions {
    display: flex;
    align-items: center;
}

.header__search {
    margin-right: var(--spacing-md);
}

.header__search-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.header__search-button:hover {
    background-color: rgba(110, 143, 232, 0.1);
    color: var(--accent-color);
}

.header__search-icon {
    width: 20px;
    height: 20px;
}

.header__auth {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header__auth-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.header__auth-link:hover {
    color: var(--primary-color);
}

.header__auth-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.header__auth-button:hover {
    background-color: var(--accent-color);
}

.header__user {
    position: relative;
}

.header__user-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.header__user-button:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--accent-color);
}

.header__user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.header__user-icon {
    width: 24px;
    height: 24px;
    background-color: currentColor;
    -webkit-mask: url('../../public/images/avatar.svg') no-repeat center;
    mask: url('../../public/images/avatar.svg') no-repeat center;
    -webkit-mask-size: contain;
    mask-size: contain;
}

.header__user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--card-background);
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px var(--shadow-color);
    min-width: 200px;
    padding: var(--spacing-sm) 0;
    margin-top: 0; /* Прибрано прогалину для безперервного переходу */
    opacity: 0;
    visibility: hidden;
    z-index: 10;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s ease 0.3s;
    pointer-events: none;
}

.header__user:hover .header__user-dropdown,
.header__user-dropdown:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s ease;
    pointer-events: auto;
}

.header__dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.3s ease;
    text-align: left;
    width: 100%;
    border: none;
    background: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.header__dropdown-item:hover {
    background-color: rgba(110, 143, 232, 0.1);
}

.header__dropdown-item--danger {
    color: var(--error-color);
}

.header__dropdown-item--danger:hover {
    background-color: rgba(230, 57, 70, 0.1);
}

.header__dropdown-divider {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: var(--spacing-xs) 0;
}

.header__mobile-menu-button {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.header__mobile-menu-button:hover {
    background-color: rgba(110, 143, 232, 0.1);
    color: var(--accent-color);
}

.header__mobile-menu-icon {
    width: 24px;
    height: 24px;
}

/* Стилі для бургер-іконки */
.burger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.burger-icon__line {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--text-color);
    border-radius: 2px;
    transition: transform 0.3s ease, opacity 0.3s ease, background-color 0.3s ease;
}

/* Анімація для активного стану бургер-іконки */
.burger-icon--active .burger-icon__line:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
    background-color: var(--primary-color);
}

.burger-icon--active .burger-icon__line:nth-child(2) {
    opacity: 0;
}

.burger-icon--active .burger-icon__line:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
    background-color: var(--primary-color);
}

/* Стилі для мобільного меню */
.header__nav--mobile-active {
    display: block;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    box-shadow: 0 4px 12px var(--shadow-color);
    padding: var(--spacing-md) 0;
    z-index: 50;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-height: calc(100vh - 60px); /* або підбери висоту заголовка */
    overflow-y: auto;

}

/* Адаптивність */
@media (max-width: 1024px) {
    .header__nav {
        display: none;
    }

    .header__mobile-menu-button {
        display: flex;
    }
}

@media (max-width: 768px) {
    .header__container {
        padding: var(--spacing-md);
    }

    .header__auth {
        display: none;
    }

    .header__mobile-menu-button {
        margin-left: auto;
    }
}

/* Медіа-запити для мобільного меню */
@media (max-width: 1024px) {
    .header__nav {
        display: none;
    }

    .header__mobile-menu-button {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .header__nav-list {
        flex-direction: column;
        width: 100%;
    }

    .header__nav-item {
        width: 100%;
    }

    .header__nav-link {
        display: block;
        width: 100%;
    }
}
