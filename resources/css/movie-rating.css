/* Movie Rating - WOW Design */
.movie-rating {
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-xl);
    background: linear-gradient(145deg, var(--card-background) 0%, rgba(var(--primary-rgb), 0.02) 100%);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    border-radius: var(--border-radius-xl);
    box-shadow:
        0 20px 60px rgba(var(--primary-rgb), 0.08),
        0 8px 25px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: hidden;
}

.movie-rating::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

.movie-rating__summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.movie-rating__average {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, rgba(var(--warning-rgb), 0.1) 0%, rgba(var(--warning-rgb), 0.05) 100%);
    border-radius: var(--border-radius-lg);
    border: 2px solid rgba(var(--warning-rgb), 0.2);
    transition: all 0.3s ease;
}

.movie-rating__average:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--warning-rgb), 0.2);
    border-color: rgba(var(--warning-rgb), 0.4);
}

.movie-rating__average-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--warning-color);
    text-shadow: 0 2px 4px rgba(var(--warning-rgb), 0.3);
    filter: drop-shadow(0 2px 4px rgba(var(--warning-rgb), 0.2));
}

.movie-rating__average-max {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-muted);
}

.movie-rating__count {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-muted);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(var(--primary-rgb), 0.08);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.movie-rating__no-ratings {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-muted);
    padding: var(--spacing-lg);
    text-align: center;
    background: rgba(var(--text-muted), 0.05);
    border-radius: var(--border-radius-lg);
    border: 2px dashed rgba(var(--text-muted), 0.2);
}

.movie-rating__user {
    padding: var(--spacing-lg);
    background: rgba(var(--background-secondary), 0.3);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--primary-rgb), 0.08);
}

.movie-rating__title {
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    justify-content: center;
}

.movie-rating__title i {
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(var(--primary-rgb), 0.3));
}

.movie-rating__user-score {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    background: linear-gradient(135deg, rgba(var(--accent-rgb), 0.1) 0%, rgba(var(--primary-rgb), 0.05) 100%);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--accent-rgb), 0.2);
    flex-wrap: wrap;
}

.movie-rating__user-score-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-color);
}

.movie-rating__user-score-value {
    font-size: var(--font-size-base);
    font-weight: 800;
    color: var(--accent-color);
    text-shadow: 0 1px 2px rgba(var(--accent-rgb), 0.3);
}

.movie-rating__review-hint {
    font-size: calc(var(--font-size-sm) * 0.9);
    font-weight: 500;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(var(--primary-rgb), 0.05);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    animation: fadeIn 0.5s ease;
}

.movie-rating__review-hint i {
    color: var(--primary-color);
    font-size: calc(var(--font-size-sm) * 0.8);
    animation: pulse 2s ease-in-out infinite;
}

.movie-rating__stars {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.movie-rating__star {
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--accent-rgb), 0.03) 100%);
    border: 2px solid rgba(var(--primary-rgb), 0.1);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: var(--spacing-xs);
    gap: 2px;
}

.movie-rating__star::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.movie-rating__star-icon {
    width: 24px;
    height: 24px;
    fill: var(--text-muted);
    stroke: none;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    flex-shrink: 0;
}

.movie-rating__star-number {
    font-size: calc(var(--font-size-sm) * 0.8);
    font-weight: 700;
    color: var(--text-muted);
    transition: all 0.3s ease;
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.movie-rating__star:hover {
    background: linear-gradient(135deg, rgba(var(--warning-rgb), 0.1) 0%, rgba(var(--warning-rgb), 0.05) 100%);
    border-color: rgba(var(--warning-rgb), 0.3);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(var(--warning-rgb), 0.2);
}

.movie-rating__star:hover::before {
    opacity: 1;
}

.movie-rating__star:hover .movie-rating__star-icon {
    fill: var(--warning-color);
    filter: drop-shadow(0 4px 8px rgba(var(--warning-rgb), 0.4));
    transform: scale(1.1);
}

.movie-rating__star--active {
    background: linear-gradient(135deg, rgba(var(--warning-rgb), 0.15) 0%, rgba(var(--warning-rgb), 0.08) 100%);
    border-color: rgba(var(--warning-rgb), 0.4);
    box-shadow: 0 4px 15px rgba(var(--warning-rgb), 0.2);
    animation: starGlow 2s ease-in-out infinite;
}

.movie-rating__star--active .movie-rating__star-icon {
    fill: var(--warning-color);
    filter: drop-shadow(0 2px 6px rgba(var(--warning-rgb), 0.4));
    animation: starPulse 2s ease-in-out infinite;
}

.movie-rating__review-form {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.03) 0%, rgba(var(--accent-rgb), 0.02) 100%);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    animation: slideInUp 0.4s ease;
}

.movie-rating__review-title {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.movie-rating__review-title i {
    color: var(--primary-color);
}

.movie-rating__review-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.movie-rating__cancel {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-muted);
    background: rgba(var(--text-muted), 0.1);
    border: 1px solid rgba(var(--text-muted), 0.2);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
}

.movie-rating__cancel:hover {
    color: var(--text-color);
    background: rgba(var(--text-muted), 0.2);
    border-color: rgba(var(--text-muted), 0.3);
    transform: translateY(-1px);
}

.movie-rating__login-prompt {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(var(--primary-rgb), 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    text-align: center;
}

.movie-rating__login-text {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.movie-rating__login-text i {
    color: var(--primary-color);
}

.movie-rating__login-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.movie-rating__login-link:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.movie-rating__review-input {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
    line-height: var(--line-height);
    color: var(--text-color);
    background: var(--white);
    border: 2px solid rgba(var(--primary-rgb), 0.1);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    resize: vertical;
}

.movie-rating__review-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    background: rgba(var(--primary-rgb), 0.01);
}

.movie-rating__submit,
.movie-rating__add-review,
.movie-rating__edit-review {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--white);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
}

.movie-rating__submit:hover,
.movie-rating__add-review:hover,
.movie-rating__edit-review:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.4);
}

.movie-rating__submit:active,
.movie-rating__add-review:active,
.movie-rating__edit-review:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
}

.movie-rating__user-review {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(var(--accent-rgb), 0.05) 0%, rgba(var(--primary-rgb), 0.03) 100%);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--accent-rgb), 0.15);
    border-left: 4px solid var(--accent-color);
    position: relative;
    overflow: hidden;
}

.movie-rating__user-review::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
    animation: pulse 2s ease-in-out infinite;
}

.movie-rating__user-review h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-base);
    font-weight: 700;
    color: var(--text-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.movie-rating__user-review p {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    line-height: var(--line-height);
    color: var(--text-color);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(var(--white), 0.5);
    border-radius: var(--border-radius-md);
    border-left: 3px solid rgba(var(--accent-rgb), 0.3);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes starGlow {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(var(--warning-rgb), 0.2);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 25px rgba(var(--warning-rgb), 0.4);
        transform: scale(1.02);
    }
}

@keyframes starPulse {
    0%, 100% {
        filter: drop-shadow(0 2px 6px rgba(var(--warning-rgb), 0.4));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 4px 12px rgba(var(--warning-rgb), 0.6));
        transform: scale(1.05);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .movie-rating {
        padding: var(--spacing-lg);
        margin: var(--spacing-lg) 0;
    }

    .movie-rating__summary {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: var(--spacing-md);
    }

    .movie-rating__average {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .movie-rating__average-value {
        font-size: 2rem;
    }

    .movie-rating__stars {
        gap: var(--spacing-xs);
    }

    .movie-rating__star {
        width: 45px;
        height: 45px;
        padding: calc(var(--spacing-sm) * 0.8);
    }

    .movie-rating__user {
        padding: var(--spacing-md);
    }

    .movie-rating__review-form {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .movie-rating__stars {
        justify-content: center;
    }

    .movie-rating__star {
        width: 40px;
        height: 40px;
        padding: calc(var(--spacing-xs) * 1.5);
    }

    .movie-rating__average-value {
        font-size: 1.8rem;
    }

    .movie-rating__title {
        font-size: var(--font-size-base);
        text-align: center;
    }
}

/* Dark Theme */
[data-theme="dark"] .movie-rating {
    background: linear-gradient(145deg, var(--dark-card-background) 0%, rgba(var(--primary-rgb), 0.02) 100%);
    border-color: rgba(var(--primary-rgb), 0.1);
}

[data-theme="dark"] .movie-rating__user {
    background: rgba(var(--dark-background), 0.3);
    border-color: rgba(var(--primary-rgb), 0.08);
}

[data-theme="dark"] .movie-rating__review-input {
    background: var(--dark-background);
    border-color: rgba(var(--primary-rgb), 0.1);
    color: var(--dark-text-color);
}

[data-theme="dark"] .movie-rating__review-input:focus {
    background: rgba(var(--primary-rgb), 0.02);
}

[data-theme="dark"] .movie-rating__user-review {
    background: linear-gradient(135deg, rgba(var(--accent-rgb), 0.08) 0%, rgba(var(--primary-rgb), 0.05) 100%);
    border-color: rgba(var(--accent-rgb), 0.2);
}

[data-theme="dark"] .movie-rating__user-review p {
    background: rgba(var(--dark-background), 0.5);
    color: var(--dark-text-color);
}
